import React, { useEffect, useState } from "react";
import { connect } from "react-redux";
import * as HostActions from "../../../Actions/HostAction";
import { socket } from "../../../Actions/HostAction";
import * as ExceptionActions from "../../../Actions/Exception";
import * as Sessions from "../../../Actions/Sessions";
import { Tooltip } from "flowbite-react";
import { getScreenSize, addScreenResizeListener } from '../../../utils/screenUtils';
import EndSessionApi, { ExtendSessionApi, UpdateSessionApi } from "../../../Tools/helpers/BackendApis";
import { GetRequestWithHeaders } from '../../../Tools/helpers/api';
import ShareModal from "../../../Tools/ToolComponents/ShareModal";
import DocumentModal from '../../../Tools/Host/DocumentModal';
import Switchproject from '../../../Tools/Host/Switchproject';
import { Firebase } from '../../../config/Firebase';
import { EyeOffIcon } from '../../../assets/SvgIcons';
import ProjectSelectionModal from "../../../components/Modals/ProjectSelectionModal";
import MobileControls from "../../../components/NewRealTImeController/MobileScreen";
import TabletControls from "../../../components/NewRealTImeController/TabScreen";
import DesktopControls from "../../../components/NewRealTImeController/DesktopScreen";
import FullscreenModal from "../../../components/Modals/FullScreenModal";
import CloseModal from "../Guest/CloseModal";

const NewRealTimeController = (props) => {
    const [modals, setModals] = useState({
        share: false,
        document: false,
        project: false,
        close: false,
        fullscreenModal: true,
        isProjectModalOpen: false,
        stopSharingModal: false,
    });

    // State for screen size
    const [screenSize, setScreenSize] = useState(getScreenSize());

    // Set up screen size listener
    useEffect(() => {
        const removeListener = addScreenResizeListener((newSize) => {
            setScreenSize(newSize);
        });

        // Clean up listener on unmount
        return () => {
            if (removeListener) removeListener();
        };
    }, []);

    useEffect(()=>{
        console.log("onMount");
        const isVideoOn = localStorage.getItem('isVideoOn') === 'true';
        const isMicOn = localStorage.getItem('isMicOn') === 'true';

        // Only handle media stream switching if needed
        // JoiningPageHost already handled the initial state setup
        if(isVideoOn && !props.Video){
          // Need to turn video on
          navigator.mediaDevices.getUserMedia({video:true}).then(stream=>{
            props.LocalStream.removeTrack(props.LocalStream.getVideoTracks()[0])
            props.LocalStream.addTrack(stream.getVideoTracks()[0]);
            props.ToogleLocalAV("Video",true)

            // Broadcast video state to other participants
            setTimeout(() => {
              socket.emit('BE-toggle-camera-audio', {
                roomId: roomId,
                switchTarget: "Video",
                value: true
              });
            }, 500);
          })
        } else if(isVideoOn && props.Video) {
          // Video should be on and is already on - just broadcast the state
          setTimeout(() => {
            socket.emit('BE-toggle-camera-audio', {
              roomId: roomId,
              switchTarget: "Video",
              value: true
            });
          }, 500);
        }

        if(isMicOn && !props.Audio){
          // Need to turn audio on
          props.LocalStream.getAudioTracks()[0].enabled=true;
          props.ToogleLocalAV("Audio",true)

          // Broadcast audio state to other participants
          setTimeout(() => {
            socket.emit('BE-toggle-camera-audio', {
              roomId: roomId,
              switchTarget: "Audio",
              value: true
            });
          }, 500);
        } else if(isMicOn && props.Audio) {
          // Audio should be on and is already on - just broadcast the state
          setTimeout(() => {
            socket.emit('BE-toggle-camera-audio', {
              roomId: roomId,
              switchTarget: "Audio",
              value: true
            });
          }, 500);
        }

        console.log("props.video",props.Video)
        console.log("props.audio",props.Audio)
    },[])
    const [fullscreen, setFullscreen] = useState(false);
    const [switchingProject, setSwitchingProject] = useState(false);

    const {
        roomId, ShowControls, Audio, Video, ScreenShare,
        LocalStream, Peers, Tab, SessionDetails, ShowSwitchProject,
        CameraAccess, MicrophoneAccess, UnreadCount, userscount, Recorder
    } = props;

    // Handle Audio Controls
    const handleAudioControl = () => {
        if (MicrophoneAccess) {
            props.ToggleUserAudio({
                roomId,
                Peers,
                Audio,
                Video,
                Screen:ScreenShare,
                LocalStream
            });
        } else {
            props.SetModalException("Mic is not Attached");
        }
    };

    // Handle Video Controls
    const handleVideoControl = () => {
        if (CameraAccess) {
            props.ToggleUserVideo({
                roomId,
                Peers,
                Audio,
                Video,
                Screen:ScreenShare,
                LocalStream
            });
        } else {
            props.SetModalException("Camera is not Attached");
        }
    };

    // Handle Screen Share
    const handleScreenShare = () => {
        props.ToggleScreenVideo({
            roomId,
            Peers,
            Audio,
            Video,
            Screen: ScreenShare,
            LocalStream
        });
    };

    // switch project
    const handleShareProject = () => {
        setModals({ ...modals, isProjectModalOpen: true });
    };

    const handleCloseModal = () => {
        setModals({ ...modals, isProjectModalOpen: false });
    };

    // Handle project selection from modal with automatic stop-share-restart flow
    const handleSelectProject = async (project) => {
        console.log("Selected project for sharing:", project);
        setSwitchingProject(true);

        try {
            // Make API call to get full project details
            const response = await GetRequestWithHeaders({
                url: process.env.REACT_APP_API_BACKEND_API_URL + `project/GetProject/${project._id}`,
            });

            console.log("Project details fetched:", response.data);

            // Determine the project type based on the selected experience type from modal
            let newProjectType = "default";

            if (project.experienceType === 'pixelstreaming') {
                // Check if it's LarkXR or regular pixel streaming
                if (response.data.projectSettings?.pixelstreaming?.application_id) {
                    newProjectType = "lark";
                } else {
                    newProjectType = "pixel_streaming";
                }
            } else if (project.experienceType === 'ale') {
                newProjectType = "ale";
            }

            // Check if we're switching from one LarkXR project to another
            const currentProjectType = SessionDetails?.type;
            const isCurrentlyLark = currentProjectType === 'lark';
            const isNewProjectLark = newProjectType === 'lark';
            const hasCurrentProject = SessionDetails?.project_id && SessionDetails.project_id !== 'undefined';

            // If switching between LarkXR projects or from LarkXR to another type, use stop-share-restart flow
            if (hasCurrentProject && (isCurrentlyLark || isNewProjectLark)) {
                console.log("Detected LarkXR project switch - using stop-share-restart flow");

                // Step 1: Stop sharing current project
                props.CreateToast({
                    message: "Switching Projects...",
                    postmessage: "Stopping current project sharing"
                });

                await handleStopSharingInternal();

                // Step 2: Wait for cleanup to complete
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Step 3: Start sharing new project
                // props.CreateToast({
                //     message: "Switching Projects...",
                //     postmessage: "Starting new project sharing"
                // });
            }

            // Now proceed with sharing the new project
            await shareNewProject(project, response.data, newProjectType);

        } catch (error) {
            console.error("Error during project switch:", error);
            props.CreateToast({
                message: "Failed to switch project",
                postmessage: "Please try again"
            });
        } finally {
            setSwitchingProject(false);
            setModals({ ...modals, isProjectModalOpen: false });
        }
    };

    // Internal stop sharing function (doesn't show toast)
    const handleStopSharingInternal = async () => {
        try {
            // Stop sharing the project by setting project_id to undefined
            const updatedSessionDetails = {
                ...SessionDetails,
                project_id: undefined,
                type: 'default',
                projectName: undefined // Clear project name
            };

            // Update the Redux store
            props.SetSessions(updatedSessionDetails);

            // Update Firestore session document
            await Firebase.firestore().collection('sessions').doc(roomId).update({
                project_id: null,
                type: 'default'
            });

            // Clear the LarkXR config data for guests
            await Firebase.firestore()
                .collection('sessions')
                .doc(roomId)
                .collection('config')
                .doc('data')
                .delete();

            // Call UpdateSession API to update backend
            await UpdateSessionApi(
                roomId,
                null, // No project ID
                'default', // Default type
                false, // Not pixel streaming
                0 // Duration
            );

            console.log("Project sharing stopped successfully");
        } catch (error) {
            console.error("Error stopping project sharing:", error);
            throw error; // Re-throw to be handled by caller
        }
    };

    // Function to share a new project
    const shareNewProject = async (project, projectData, projectType) => {
    // Check if session is currently active
    const currentTime = new Date().getTime();
    const startTime = new Date(SessionDetails.schedule_time || SessionDetails.start).getTime();
    const endTime = new Date(SessionDetails.end_time).getTime();
    
    const isSessionActive = currentTime >= startTime && currentTime < endTime;
    const isSessionEnded = currentTime >= endTime;
    const isSessionNotStarted = currentTime < startTime;

    // Update the session details with the new project type and name
    const updatedSessionDetails = {
        ...SessionDetails,
        project_id: project._id,
        projectName: project.name,
        type: projectType
    };

    // Update the Redux store with the new project details and session details
    props.SetProjectDetails(projectData);
    props.SetSessions(updatedSessionDetails);

    // Update Firestore with the new project_id, name, and type
    await Firebase.firestore().collection('sessions').doc(roomId).update({
        project_id: project._id,
        projectName: project.name,
        type: projectType
    });

    // Call the UpdateSession API to update the session type on the backend
    // Determine if pixel streaming is active based on project type
    const isPixelstreamingActive = projectType === 'pixel_streaming' || projectType === 'lark';

    await UpdateSessionApi(
        roomId,
        project._id,
        projectType,
        isPixelstreamingActive,
        0
    );

    // Show appropriate message based on session timing
    if (isSessionActive) {
        props.CreateToast({
            message: "Project Shared Successfully",
            postmessage: `Shared with ${project.name}`
        });
    } else if (isSessionEnded) {
        props.CreateToast({
            message: "Session Ended",
            postmessage: `${project.name} will be available when session is extended`
        });
    } else if (isSessionNotStarted) {
        props.CreateToast({
            message: "Session Not Started",
            postmessage: `${project.name} will be available when session starts`
        });
    }
};

    // Handle stopping project sharing (direct action without modal)
const handleStopSharing = async () => {
    console.log("Stop sharing clicked - executing directly");
    try {
        await handleStopSharingInternal();
        props.CreateToast({
            message: "Project Sharing Stopped",
            postmessage: "Project has been removed from the session"
        });
    } catch (error) {
        console.error("Error stopping sharing:", error);
        props.CreateToast({
            message: "Error stopping sharing",
            postmessage: "Please try again"
        });
    }
};

    // Handle Ending Session (direct action without modal)
const handleEndSession = () => {
    console.log("End session clicked - executing directly");
    if (Recorder) {
        props.SetModalException("Recording is still on. Please stop the recording before closing this session");
    } else {
        console.log("Ending session directly");
        closeSession();
    }
};

const handleEndSessionWithModal = () => {
  setModals(prev => ({ ...prev, close: true }));
};
const handleStopSharingWithModal = () => {
  setModals(prev => ({ ...prev, stopSharingModal: true }));
};

    // Close session API call and redirect
    const closeSession = () => {
        EndSessionApi(roomId);
        window.location.href = `/sessions`;
    };

    // Toggle fullscreen mode
    const toggleFullscreen = (skipConfirmation = false) => {
        // If skipConfirmation is true, directly toggle fullscreen
        // Otherwise show the confirmation modal first
        if (skipConfirmation) {
            setModals({ ...modals, fullscreenModal: false });
            toggleFullscreenMode();
        } else {
            setModals({ ...modals, fullscreenModal: true, share: false });
        }
    };

    const toggleFullscreenMode = () => {
        if (!fullscreen || window.innerHeight !== window.screen.height) {
            setFullscreen(true);

            try {
                // Try using document.documentElement
                if (document.documentElement.requestFullscreen) {
                    document.documentElement.requestFullscreen();
                } else if (document.documentElement.mozRequestFullScreen) {
                    document.documentElement.mozRequestFullScreen();
                } else if (document.documentElement.webkitRequestFullscreen) {
                    document.documentElement.webkitRequestFullscreen();
                } else if (document.documentElement.msRequestFullscreen) {
                    document.documentElement.msRequestFullscreen();
                }
            } catch (error) {
                console.error("Fullscreen error:", error);
            }
        } else {
            setFullscreen(false);

            try {
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.mozCancelFullScreen) {
                    document.mozCancelFullScreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
            } catch (error) {
                console.error("Exit fullscreen error:", error);
            }
        }
    };

    // Toggle modal visibility
    const toggleModal = (modalName, isVisible) => {
        setModals({ ...modals, [modalName]: isVisible });
    };

    // Control tab visibility
    const handleTabControl = (tabName) => {
        if (tabName === "CHAT") {
            // Toggle chat sidebar
            props.ToggleChat();

            // Reset unread count when opening chat tab
            if (UnreadCount > 0) {
                props.SetUnreadMessage(0);
            }
        } else if (tabName === "MEMBERS") {
            // Toggle members sidebar
            props.ToggleMembers();
        }
    };

    // Render the floating control button that's always at the top right
    const renderFloatingControls = () => (
        !ShowControls && (
            <div className="fixed z-50 top-6 right-[70px] md:right-[100px]">
                <Tooltip
                    arrow={7.5}
                    background="rgb(0 0 0 / 0.1)"
                    border="#fff"
                    color="#fff"
                    content="Show Controls"
                    fadeDuration={0}
                    fadeEasing="linear"
                    fixed={false}
                    fontFamily="inherit"
                    fontSize="1rem"
                    offset={0}
                    padding={3}
                    direction="down"
                    radius={4}
                    zIndex={1}
                >
                    <button
                        onClick={() => props.INVERT_CONTROLS(ShowControls)}
                        className="p-2 rounded-lg bg-[#F3F4F6] hover:bg-[#E5E7EB] shadow-lg transition-all duration-300"
                    >
                        <EyeOffIcon />
                    </button>
                </Tooltip>
            </div>
        )
    );

    // Render fullscreen modal
    const renderFullscreenModal = () => (
        modals.fullscreenModal && !modals.share && (
            <FullscreenModal
                onAccept={toggleFullscreenMode}
                onDecline={() => toggleModal('fullscreenModal', false)}
            />
        )
    );

    // Render modals
const renderModals = () => (
    <>
        {/* Share Modal */}
        {modals.share && (
            <ShareModal
                open_close={(name, flag) => toggleModal(name, flag)}
                share={modals.share}
                pid={props.roompid}
                roomId={roomId}
                user_id={Firebase.auth().currentUser.uid}
            />
        )}

        {/* Project Selection Modal */}
        {modals.isProjectModalOpen && (
            <ProjectSelectionModal
                isOpen={modals.isProjectModalOpen}
                onClose={handleCloseModal}
                onSelectProject={handleSelectProject}
                currentProjectId={SessionDetails?.project_id}
                currentProjectType={SessionDetails?.type}
            />
        )}

        {/* Document Modal */}
        {props.data && modals.document && (
            <DocumentModal
                senddata={props.senddata}
                room={roomId}
                data={props.data}
                open_close={(name, flag) => toggleModal(name, flag)}
                document={modals.document}
            />
        )}

        {/* Switch Project Modal */}
        {modals.project && (
            <Switchproject
                switch={props.switch}
                changeProject={props.changeProject}
                data={props.data}
                open_close={(name, flag) => toggleModal(name, flag)}
                project={modals.project}
                pid={props.pid}
                user_id={Firebase.auth().currentUser.uid}
            />
        )}

        {/* End Session Modal */}
        {modals.close && (
            <CloseModal
                type="exit"
                CloseSession={closeSession}
                Close={() => {
                    console.log("Closing end session modal");
                    setModals(prev => ({ ...prev, close: false }));
                }}
            />
        )}

        {/* Stop Sharing Modal */}
        {modals.stopSharingModal && (
            <CloseModal
                type="stopSharing"
                CloseSession={async () => {
                    console.log("Confirming stop sharing");
                    try {
                        await handleStopSharingInternal();
                        setModals(prev => ({ ...prev, stopSharingModal: false }));
                        props.CreateToast({
                            message: "Project Sharing Stopped",
                            postmessage: "Project has been removed from the session"
                        });
                    } catch (error) {
                        console.error("Error stopping sharing:", error);
                        props.CreateToast({
                            message: "Error stopping sharing",
                            postmessage: "Please try again"
                        });
                    }
                }}
                Close={() => {
                    console.log("Canceling stop sharing");
                    setModals(prev => ({ ...prev, stopSharingModal: false }));
                }}
            />
        )}
    </>
);

    // Main render
    return (
        <>
            {renderFloatingControls()}

            {/* Desktop Controls */}
            {ShowControls && screenSize === 'desktop' && (
                <DesktopControls
                    Audio={Audio}
                    Video={Video}
                    Tab={Tab}
                    ChatOpen={props.ChatOpen}
                    MembersOpen={props.MembersOpen}
                    UnreadCount={UnreadCount}
                    userscount={userscount + 1}
                    handleAudioControl={handleAudioControl}
                    handleVideoControl={handleVideoControl}
                    handleEndSessionWithModal={handleEndSessionWithModal}
                    handleStopSharingWithModal={handleStopSharingWithModal}
                    handleTabControl={handleTabControl}
                    handleEndSession={handleEndSession}
                    handleStopSharing={handleStopSharing}
                    toggleFullscreen={toggleFullscreen}
                    handleInvertControls={(showControls) => props.INVERT_CONTROLS(showControls)}
                    ShowControls={ShowControls}
                    handleShareProject={handleShareProject}
                    handleScreenShare={handleScreenShare}
                    ScreenShare={ScreenShare}
                    switchingProject={switchingProject}
                    SessionDetails={SessionDetails}
                    roomId={roomId}
                />
            )}

            {/* Mobile Controls */}
            {ShowControls && screenSize === 'mobile' && (
                <div>
                    <MobileControls
                        Audio={Audio}
                        Video={Video}
                        Tab={Tab}
                        ChatOpen={props.ChatOpen}
                        MembersOpen={props.MembersOpen}
                        UnreadCount={UnreadCount}
                        userscount={userscount + 1}
                        handleAudioControl={handleAudioControl}
                        handleVideoControl={handleVideoControl}
                        handleTabControl={handleTabControl}
                        handleEndSession={handleEndSession}
                        handleStopSharing={handleStopSharing}
                        handleEndSessionWithModal={handleEndSessionWithModal}
                    handleStopSharingWithModal={handleStopSharingWithModal}
                        toggleFullscreen={toggleFullscreen}
                        handleInvertControls={(showControls) => props.INVERT_CONTROLS(showControls)}
                        ShowControls={ShowControls}
                        handleShareProject={handleShareProject}
                        handleScreenShare={handleScreenShare}
                        ScreenShare={ScreenShare}
                        switchingProject={switchingProject}
                        SessionDetails={SessionDetails}
                        roomId={roomId}
                    />
                </div>
            )}

            {/* Tablet Controls */}
            {ShowControls && screenSize === 'tablet' && (
                <div>
                    <TabletControls
                        Audio={Audio}
                        Video={Video}
                        Tab={Tab}
                        ChatOpen={props.ChatOpen}
                        MembersOpen={props.MembersOpen}
                        UnreadCount={UnreadCount}
                        userscount={userscount + 1}
                        handleAudioControl={handleAudioControl}
                        handleVideoControl={handleVideoControl}
                        handleTabControl={handleTabControl}
                        handleEndSession={handleEndSession}
                        handleStopSharing={handleStopSharing}
                        toggleFullscreen={toggleFullscreen}
                        handleEndSessionWithModal={handleEndSessionWithModal}
                    handleStopSharingWithModal={handleStopSharingWithModal}
                        handleInvertControls={(showControls) => props.INVERT_CONTROLS(showControls)}
                        ShowControls={ShowControls}
                        handleShareProject={handleShareProject}
                        handleScreenShare={handleScreenShare}
                        ScreenShare={ScreenShare}
                        switchingProject={switchingProject}
                        SessionDetails={SessionDetails}
                        roomId={roomId}
                    />
                </div>
            )}

            {renderFullscreenModal()}
            {renderModals()}
        </>
    );
};

const mapStateToProps = state => {
    return {
        Peers: state.Call.peers,
        SocketId: state.Call.SocketId,
        LocalStream: state.Call.LocalStream,
        UserName: state.Call.UserName,
        Video: state.Call.Video,
        Audio: state.Call.Audio,
        ScreenShare: state.Call.ScreenShare,
        Recorder: state.Call.Recorder,
        CameraAccess: state.Call.HasCamera,
        MicrophoneAccess: state.Call.HasMicrophone,
        Tab: state.Call.Tab,
        ChatOpen: state.Call.ChatOpen,
        MembersOpen: state.Call.MembersOpen,
        ShowControls: state.Call.ShowControls,
        userscount: state.Call.userscount,
        UnreadCount: state.Call.UnreadCount,
        SessionDetails: state.Sessions.sessions,
        ProjectDetails: state.Sessions.projectDetails,
    };
};

const mapDispatchToProps = {
    ...HostActions,
    ...ExceptionActions,
    ...Sessions
};

export default connect(mapStateToProps, mapDispatchToProps)(NewRealTimeController);