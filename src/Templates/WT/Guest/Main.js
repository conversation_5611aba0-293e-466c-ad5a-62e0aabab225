import React from 'react';
import { connect } from 'react-redux';
import '../../../styles/video.css';
import * as HostActions from "../../../Actions/HostAction";
import {socket,peersRef} from "../../../Actions/HostAction"
import * as ExceptionActions from "../../../Actions/Exception"
import TypeSwitch from './TypeSwitch';
import { getScreenSize, addScreenResizeListener } from '../../../utils/screenUtils';
// import Participants from './Participants';
import ChatSidebar from './ChatSidebar';
import NewRealTImeController from './NewRealTimeController';
import SideBar from './SideBar';
import { Tooltip } from "flowbite-react";
import { EyeOffIcon } from '../../../assets/SvgIcons';
import ScreenShareDebug from '../../../components/ScreenShareDebug';

var roomId;

class Room extends React.Component{
  constructor(props){
    super(props);
    this.state={
      upload:0,
      recorder:false,
      recordertime:false,
      platform:window.vuplex?false:true,
      fullscreen:true,
      screenSize: getScreenSize(),
      isScreenSharingPinned: false // Pin state for screen sharing
    }
    // Removed unused refs
    this.PinVideo= React.createRef();
    this.SendDatabyChannel=this.SendDatabyChannel.bind(this);
    this.SendDatabyChannelByType=this.SendDatabyChannelByType.bind(this);
    this.DataChannelMessage=this.DataChannelMessage.bind(this)

    roomId = this.props.roomId;
    this.findPeer=this.findPeer.bind(this);
    this.togglePin=this.togglePin.bind(this);
  }

  // Toggle pin state for screen sharing
  togglePin() {
    this.setState(prevState => {
      const newPinnedState = !prevState.isScreenSharingPinned;

      // If screen sharing is being unpinned (moving to participants tab)
      // and participants tab is not open, automatically open it
      if (prevState.isScreenSharingPinned && !newPinnedState && !this.props.MembersOpen) {
        // Open participants tab so user can see the screen sharing content
        this.props.ToggleMembers(true);
      }

      return {
        isScreenSharingPinned: newPinnedState
      };
    });
  }


  // Handle data channel messages
  DataChannelMessage(Peer, data){
    // Keeping this method for compatibility with existing code
    // The parameters are used in the host version
    console.log('Data channel message received:', { from: Peer, data });
  }
componentDidMount(){
    // Set up screen size listener
    this.removeScreenSizeListener = addScreenResizeListener((newSize) => {
      this.setState({ screenSize: newSize });
    });

    this.props.SetVideoDevices()
const Avatarsrc=["https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar1.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar2.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar3.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar4.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar5.png",
"https://cdn.glitch.me/a04a26d3-92af-4a88-9d49-8fcc2c5344a5%2Favatar6.png",
]
const extra={color:Math.floor(Math.random()*16777215).toString(16),username:this.props.UserName,type:"Guest",id:socket.id,Avatar:Avatarsrc[Math.floor(Math.random() * 5)]}

  window.addEventListener('beforeunload', (e) => {
    e.preventDefault();
    socket.emit('BE-leave-room', { roomId, leaver: this.props.UserName });
    sessionStorage.removeItem('user');
    window.location.href = '/';
  });
 // Check if LocalVideo element exists before setting srcObject
 const localVideoElement = document.getElementById("LocalVideo");
 if (localVideoElement) {
   localVideoElement.srcObject = this.props.LocalStream;
 }
 this.props.Call(this.props.LocalStream,this.DataChannelMessage);
 socket.on('FE-user-leave', ({ userId, userName }) => {
        // Handle user leaving the room
        console.log(`User ${userName} with ID ${userId} left the room`);
        const peerIdx = this.findPeer(userId);
        if(peerIdx){
          peerIdx.peer.destroy();
          if(peerIdx.peer.extra.type=="host")
        this.props.SetHostStatus(false)
         this.props.UpdatePeer(this.props.Peers,userId)
        }

      });
  // Ensure userVideoAudio state is properly set before joining the room
  this.props.SetUserAV("localUser", {
    video: this.props.Video,
    audio: this.props.Audio,
    screen: false
  });

  // Now join the room with the current audio/video state
  socket.emit('BE-join-room', {
    roomId,
    userName: this.props.UserName,
    extra: JSON.stringify(extra),
    audio: this.props.Audio,
    video: this.props.Video,
    screen: false
  });

  // Force broadcast actual video/audio state after joining
  // This ensures other participants see the correct initial state
  setTimeout(() => {
    console.log('Guest broadcasting actual state:', { video: this.props.Video, audio: this.props.Audio });

    // Broadcast current video state to all participants
    socket.emit('BE-toggle-camera-audio', {
      roomId: roomId,
      switchTarget: "Video",
      value: this.props.Video
    });

    // Broadcast current audio state to all participants
    socket.emit('BE-toggle-camera-audio', {
      roomId: roomId,
      switchTarget: "Audio",
      value: this.props.Audio
    });
  }, 1000); // Small delay to ensure room join is complete

  // Subscribe to chat messages at the Main component level
  // This ensures messages are received even when the chat sidebar is closed
  HostActions.SubscribeToChat((msg) => {
    const data = JSON.parse(msg);
    // Update unread count if chat is not open
    if (!this.props.ChatOpen) {
      this.props.SetUnreadMessage(this.props.UnreadCount + 1);
    }
    this.props.SetMessage(data);
  });
}

componentWillUnmount() {
  // Clean up screen size listener
  if (this.removeScreenSizeListener) {
    this.removeScreenSizeListener();
  }

  // Unsubscribe from chat events
  HostActions.UnsubscribeFromChat();
}







findPeer(id) {
  return peersRef.find((p) => p.peerID === id);
}
VideoControl(){
  if(this.props.CameraAccess){
    this.props.ToggleUserVideo({roomId:roomId,
      Peers:this.props.Peers,
      Audio:this.props.Audio,
      Video:this.props.Video,
      Screen:this.props.ScreenShare,
      LocalStream:this.props.LocalStream})
     }
     else{
  this.props.SetModalException("Camera is not Attached")
}
}

SendDatabyChannel(Data){
  Object.values(this.props.Peers).map(Peer=>{
    if(Peer && Peer._channel){
       if(Peer._channel.readyState=="open")
    Peer.send(JSON.stringify(Data))
    }
  })
}


SendDatabyChannelByType(Data,Type=false){
  Object.values(this.props.Peers).map(Peer=>{
    if(Peer && Peer._channel){
      if(!Type){
        if(Peer._channel.readyState=="open")
        Peer.send(JSON.stringify(Data))
      }else{
        if(Peer.extra.type==Type)
        if(Peer._channel.readyState=="open")
        Peer.send(JSON.stringify(Data))
      }
    }
  })
}

toHumanReadableTime(isoString) {
  const date = new Date(isoString);

  const formattedDate = `${date.toLocaleTimeString()} on ${date.toDateString()}`;

  return formattedDate;
}

render(){
    return (
      <>
        <div className="relative flex flex-col h-full w-full overflow-hidden" id="MainContentDiv">
          {/* Unhide button that's always visible when controls are hidden */}
          {!this.props.ShowControls && (
            <div className="fixed z-[9999] top-6 right-6">
              <Tooltip
                arrow={7.5}
                background="rgb(0 0 0 / 0.1)"
                border="#fff"
                color="#fff"
                content="Show Controls"
                fadeDuration={0}
                fadeEasing="linear"
                fixed={false}
                fontFamily="inherit"
                fontSize="1rem"
                offset={0}
                padding={3}
                direction="down"
                radius={4}
                zIndex={1}
              >
                <button
                  onClick={() => {
                    console.log('Unhide button clicked, current ShowControls:', this.props.ShowControls);
                    this.props.TOGGLE_CONTROLS(true);
                  }}
                  className="p-2 rounded-lg bg-white hover:bg-gray-100 shadow-lg transition-all duration-300 border border-gray-200"
                >
                  <EyeOffIcon />
                </button>
              </Tooltip>
            </div>
          )}

          {/* Controller at the top in normal document flow */}
          {this.props.ShowControls && (
            <div className="w-full">
              <NewRealTImeController
                roomId={this.props.roomId}
                isFullscreen={this.state.fullscreen}
              />
            </div>
          )}
          {/* Content area that adjusts based on controller visibility */}
          <div className="flex flex-row w-full flex-grow overflow-hidden">
            <div className={`relative ${this.state.screenSize === 'desktop' && (this.props.ChatOpen) ? 'w-[calc(100%-350px)]' : 'w-full'} h-full transition-all duration-300`}>
              <TypeSwitch
                platform={this.state.platform}
                SendDatabyChannelByType={this.SendDatabyChannelByType}
                SendDatabyChannel={this.SendDatabyChannel}
                SubscribeToCustom={HostActions.SubscribeToCustom}
                SendCustomMessage={this.props.SendCustomMessage}
                roomId={this.props.roomId}
                isScreenSharingPinned={this.state.isScreenSharingPinned}
                onTogglePin={this.togglePin}
              />

              {/* Upload modal */}
              {this.state.upload && (
                <div className="modal" id="admit_modal" tabIndex={-1} role="dialog" style={{ display: 'block' }}>
                  <div className="modal-dialog" role="document">
                    <div className="modal-content">
                      <div className="modal-header">
                        <h5 style={{ color: '#222b45' }} className="modal-title">Uploading the recorded Content</h5>
                      </div>
                      <div className="modal-body">
                        <p>Don't close the tab until it gets uploaded</p>
                        <p className="share_content">
                          <div className="progress skeleton-box" style={{width:"100%",height:"15px"}}>
                            <div className="progress-bar" role="progressbar" style={{width: this.state.upload+"%",backgroundColor:"var(--main-color)",height:"15px"}} aria-valuenow={25} aria-valuemin={0} aria-valuemax={100} />
                          </div>
                          <span style={{float: 'right', flex: 'auto', maxWidth: 'fit-content', marginLeft:"8px",marginTop:"8px"}}>{this.state.upload.toFixed(0)+"%"}</span>
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>

            {/* Participants component */}
            {/* {this.props.MembersOpen && (
              <Participants
                isMobile={false}
                roomId={this.props.roomId}
                Tab={this.props.Tab}
                onClose={() => this.props.ToggleMembers(false)}
              />
            )} */}

            {/* Chat sidebar */}
            {this.props.ChatOpen && (
              <div className="h-full relative z-[999999]">
                <ChatSidebar
                  socketId={socket.id}
                  roomId={this.props.roomId}
                  isMobile={false}
                />
              </div>
            )}

            <SideBar sendmessage={this.sendmessage} PinVideo={this.PinVideo} socketid={socket.id} roomId={this.props.roomId} Sidenav={this.Sidenav} onTogglePin={this.togglePin} isScreenSharingPinned={this.state.isScreenSharingPinned} />

          </div>
        </div>
      </>
    );
  }
};



const mapStateTothisprops = state => {
  return {
    Peers: state.Call.peers,
    VideoDevices: state.Call.VideoDevices,
    SocketId: state.Call.SocketId,
    LocalStream: state.Call.LocalStream,
    UserName: state.Call.UserName,
    userVideoAudio: state.Call.userVideoAudio,
    Video: state.Call.Video,
    Audio: state.Call.Audio,
    Tab: state.Call.Tab,
    ChatOpen: state.Call.ChatOpen,
    MembersOpen: state.Call.MembersOpen,
    UnreadCount: state.Call.UnreadCount,
    ShowControls: state.Call.ShowControls,
  }
}

const mapDispatchTothisprops = {
  ...HostActions,
  ...ExceptionActions
}

export default connect(mapStateTothisprops, mapDispatchTothisprops)(Room)